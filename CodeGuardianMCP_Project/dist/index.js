#!/usr/bin/env node
/**
 * CodeGuardian MCP - Model Context Protocol para análisis estático de código
 *
 * Este es el punto de entrada principal del servidor MCP que proporciona
 * capacidades avanzadas de análisis estático de código para múltiples lenguajes.
 *
 * Características principales:
 * - Análisis de sintaxis para Python, TypeScript/JavaScript
 * - Validación de imports y dependencias
 * - Verificación de rutas de archivos
 * - Reportes detallados con sugerencias de mejora
 * - Configuración flexible y extensible
 *
 * <AUTHOR> MCP Team
 * @version 1.0.0
 */
import { CodeGuardianServer } from './server.js';
/**
 * Función principal para iniciar el servidor MCP
 */
async function main() {
    try {
        // Mostrar información de inicio
        console.error('🚀 Iniciando CodeGuardian MCP...');
        console.error('📋 Model Context Protocol para análisis estático de código');
        console.error('🔧 Versión: 1.0.0');
        console.error('');
        // Crear e iniciar el servidor
        const server = new CodeGuardianServer();
        await server.start();
        // El servidor ahora está ejecutándose y esperando conexiones MCP
        console.error('✅ Servidor listo para recibir conexiones MCP');
    }
    catch (error) {
        console.error('❌ Error fatal iniciando CodeGuardian MCP:', error);
        process.exit(1);
    }
}
/**
 * Manejo de señales del sistema para cierre limpio
 */
function setupSignalHandlers() {
    const signals = ['SIGINT', 'SIGTERM', 'SIGQUIT'];
    for (const signal of signals) {
        process.on(signal, () => {
            console.error(`\n📡 Señal ${signal} recibida, cerrando servidor...`);
            console.error('👋 CodeGuardian MCP terminado');
            process.exit(0);
        });
    }
}
/**
 * Manejo de errores no capturados
 */
function setupErrorHandlers() {
    process.on('uncaughtException', (error) => {
        console.error('💥 Excepción no capturada:', error);
        console.error('🔄 Reiniciando servidor...');
        process.exit(1);
    });
    process.on('unhandledRejection', (reason, promise) => {
        console.error('🚫 Promise rechazada no manejada:', reason);
        console.error('📍 En promise:', promise);
        console.error('🔄 Reiniciando servidor...');
        process.exit(1);
    });
}
/**
 * Configuración inicial del entorno
 */
function setupEnvironment() {
    // Configurar timezone si no está definido
    if (!process.env.TZ) {
        process.env.TZ = 'UTC';
    }
    // Configurar encoding por defecto
    if (!process.env['NODE_OPTIONS']?.includes('--input-type')) {
        process.env['NODE_OPTIONS'] = (process.env['NODE_OPTIONS'] || '') + ' --input-type=module';
    }
}
/**
 * Validación de requisitos del sistema
 */
function validateSystemRequirements() {
    // Verificar versión de Node.js
    const nodeVersion = process.version;
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0] || '0');
    if (majorVersion < 18) {
        console.error(`❌ Node.js ${nodeVersion} no es compatible`);
        console.error('📋 Se requiere Node.js 18.0.0 o superior');
        process.exit(1);
    }
    // Verificar que estamos en modo módulo ES
    // En ES modules, no necesitamos verificar require.main
    console.log('✅ Ejecutándose en modo ES modules');
}
/**
 * Mostrar información de ayuda si se solicita
 */
function showHelpIfRequested() {
    const args = process.argv.slice(2);
    if (args.includes('--help') || args.includes('-h')) {
        console.log(`
🛡️  CodeGuardian MCP - Analizador Estático de Código

DESCRIPCIÓN:
  Model Context Protocol que proporciona análisis estático avanzado
  de código fuente para múltiples lenguajes de programación.

USO:
  node dist/index.js                 # Iniciar servidor MCP
  npm run dev                        # Modo desarrollo
  npm run build && npm start         # Modo producción

LENGUAJES SOPORTADOS:
  🐍 Python (.py, .pyi)
  📘 TypeScript (.ts, .tsx)
  📜 JavaScript (.js, .jsx, .mjs, .cjs)
  📋 JSON (.json)

CARACTERÍSTICAS:
  ✅ Análisis de sintaxis
  ✅ Validación de imports
  ✅ Verificación de rutas
  ✅ Análisis de dependencias
  ✅ Reportes detallados
  ✅ Configuración flexible

HERRAMIENTAS MCP DISPONIBLES:
  • analyze_project        - Analizar proyecto completo
  • analyze_file          - Analizar archivo individual
  • get_supported_languages - Listar lenguajes soportados
  • validate_config       - Validar configuración
  • get_analyzer_stats    - Estadísticas de analizadores

EJEMPLOS DE USO:
  # Conectar desde un cliente MCP
  mcp connect stdio node dist/index.js

  # Usar con desktop-commander
  desktop-commander add-mcp codeguardian node dist/index.js

DOCUMENTACIÓN:
  README.md - Documentación completa
  examples/ - Ejemplos de uso

SOPORTE:
  GitHub: https://github.com/codeguardian-mcp
  Issues: https://github.com/codeguardian-mcp/issues
`);
        return true;
    }
    return false;
}
/**
 * Punto de entrada principal
 */
if (import.meta.url === `file://${process.argv[1]}`) {
    // Configurar entorno
    setupEnvironment();
    // Validar requisitos
    validateSystemRequirements();
    // Mostrar ayuda si se solicita
    if (showHelpIfRequested()) {
        process.exit(0);
    }
    // Configurar manejadores
    setupSignalHandlers();
    setupErrorHandlers();
    // Iniciar servidor
    main().catch((error) => {
        console.error('💥 Error fatal en main():', error);
        process.exit(1);
    });
}
// Exportar para uso como módulo
export { CodeGuardianServer } from './server.js';
export * from './types.js';
export * from './config/schemas.js';
//# sourceMappingURL=index.js.map