/**
 * Archivo de ejemplo TypeScript para demostrar el análisis de CodeGuardian MCP
 * Este archivo contiene varios tipos de issues intencionalmente.
 */

import { NonExistentModule } from './non-existent-module';  // Error: módulo no existe
import fs from 'fs';
import path from 'path';

// Uso del tipo 'any' (warning)
function processData(data: any): any {
    return data.someProperty;
}

// Import múltiple sin especificar tipos
import { Component, useState, useEffect } from 'react';

// Función con error de sintaxis - falta punto y coma
function badSyntax() {
    const message = "Hello world"  // Falta punto y coma
    return message;
}

// Paréntesis no balanceados
function unbalancedBraces() {
    const obj = {
        name: 'test',
        value: 42
    // Falta cerrar llave
}

// Referencia a archivo que no existe
function loadConfig(): string {
    const configPath = path.join(__dirname, './config/missing-file.json');
    return fs.readFileSync(configPath, 'utf-8');
}

// Interfaz correcta
interface UserData {
    id: number;
    name: string;
    email: string;
    isActive: boolean;
}

// Función correcta
function createUser(userData: UserData): UserData {
    return {
        ...userData,
        id: Math.floor(Math.random() * 1000)
    };
}

// Componente JSX sin import de React (warning)
function MyComponent(): JSX.Element {
    const [count, setCount] = useState(0);
    
    useEffect(() => {
        console.log('Component mounted');
    }, []);

    return (
        <div>
            <h1>Count: {count}</h1>
            <button onClick={() => setCount(count + 1)}>
                Increment
            </button>
        </div>
    );
}

// Función que usa require (para testing)
function legacyImport() {
    const lodash = require('lodash');  // Posible warning sobre mixing import styles
    return lodash.isEmpty({});
}

export { createUser, MyComponent, processData };
