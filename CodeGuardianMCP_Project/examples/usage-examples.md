# Ejemplos de Uso de CodeGuardian MCP

Este documento proporciona ejemplos prácticos de cómo usar CodeGuardian MCP en diferentes escenarios.

## 1. Análisis Básico de Proyecto

### Comando MCP
```json
{
  "name": "analyze_project",
  "arguments": {
    "project_path": "/path/to/your/project"
  }
}
```

### Respuesta Esperada
```json
{
  "success": true,
  "data": {
    "summary": {
      "files_scanned": 25,
      "errors_found": 3,
      "warnings_found": 7,
      "info_found": 2,
      "languages_detected": ["python", "typescript"],
      "scan_duration_ms": 1250,
      "timestamp": "2024-01-15T10:30:45.123Z"
    },
    "issues": [...]
  }
}
```

## 2. Análisis con Configuración Personalizada

### Comando MCP
```json
{
  "name": "analyze_project",
  "arguments": {
    "project_path": "./src",
    "config": {
      "languages": ["python", "typescript"],
      "include_patterns": ["**/*.py", "**/*.ts"],
      "exclude_patterns": ["**/test/**", "**/node_modules/**"],
      "rules": {
        "syntax_check": true,
        "import_validation": true,
        "path_validation": false,
        "style_check": true
      },
      "output_format": "json"
    }
  }
}
```

## 3. Análisis de Archivo Individual

### Comando MCP
```json
{
  "name": "analyze_file",
  "arguments": {
    "file_path": "./src/utils/helpers.py",
    "language": "python"
  }
}
```

### Respuesta Esperada
```json
{
  "success": true,
  "data": {
    "file_info": {
      "path": "./src/utils/helpers.py",
      "language": "python",
      "size_bytes": 1024,
      "lines_count": 45,
      "encoding": "utf-8"
    },
    "issues": [
      {
        "type": "ImportError",
        "severity": "error",
        "message": "No se puede resolver el import 'non_existent_module'",
        "file_path": "./src/utils/helpers.py",
        "line": 5,
        "column": 7,
        "code_snippet": "import non_existent_module",
        "suggestion": "Verifica que el módulo esté instalado o que la ruta sea correcta",
        "language": "python"
      }
    ],
    "analysis_duration_ms": 150,
    "summary": {
      "total_issues": 1,
      "errors": 1,
      "warnings": 0,
      "info": 0
    }
  }
}
```

## 4. Obtener Lenguajes Soportados

### Comando MCP
```json
{
  "name": "get_supported_languages",
  "arguments": {}
}
```

### Respuesta Esperada
```json
{
  "success": true,
  "data": {
    "supported_languages": ["python", "typescript", "javascript", "json"],
    "analyzer_stats": {
      "total_analyzers": 3,
      "analyzer_details": [
        {
          "language": "python",
          "analyzer_class": "PythonAnalyzer"
        },
        {
          "language": "typescript",
          "analyzer_class": "TypeScriptAnalyzer"
        },
        {
          "language": "javascript",
          "analyzer_class": "TypeScriptAnalyzer"
        }
      ]
    }
  }
}
```

## 5. Validar Configuración

### Comando MCP
```json
{
  "name": "validate_config",
  "arguments": {
    "config": {
      "target_path": "./src",
      "languages": ["python", "typescript"],
      "rules": {
        "syntax_check": true,
        "import_validation": true
      },
      "performance": {
        "max_concurrent_files": 5,
        "timeout_per_file_ms": 10000
      }
    }
  }
}
```

### Respuesta Esperada
```json
{
  "success": true,
  "data": {
    "message": "Configuración válida",
    "validated_config": {
      "target_path": "./src",
      "include_patterns": ["**/*.py", "**/*.ts", "**/*.js", "**/*.json"],
      "exclude_patterns": ["**/node_modules/**", "**/.git/**", "**/dist/**", "**/__pycache__/**"],
      "max_file_size_mb": 10,
      "languages": ["python", "typescript"],
      "rules": {
        "syntax_check": true,
        "import_validation": true,
        "path_validation": true,
        "dependency_check": true,
        "style_check": false
      },
      "output_format": "both"
    }
  }
}
```

## 6. Configuración Avanzada para Proyectos Grandes

### Comando MCP
```json
{
  "name": "analyze_project",
  "arguments": {
    "project_path": "/large/project",
    "config": {
      "languages": ["python", "typescript", "javascript"],
      "include_patterns": [
        "src/**/*.py",
        "frontend/**/*.ts",
        "frontend/**/*.tsx",
        "api/**/*.js"
      ],
      "exclude_patterns": [
        "**/node_modules/**",
        "**/dist/**",
        "**/build/**",
        "**/__pycache__/**",
        "**/coverage/**",
        "**/.pytest_cache/**"
      ],
      "max_file_size_mb": 2,
      "performance": {
        "max_concurrent_files": 3,
        "timeout_per_file_ms": 20000,
        "memory_limit_mb": 1024
      },
      "reporting": {
        "include_code_snippets": false,
        "group_by_file": true,
        "sort_by_severity": true
      },
      "language_configs": {
        "python": {
          "python_version": "3.9",
          "max_line_length": 100,
          "ignore_missing_imports": ["tensorflow", "torch"]
        },
        "typescript": {
          "strict_mode": true,
          "tsconfig_path": "./frontend/tsconfig.json"
        }
      }
    }
  }
}
```

## 7. Integración con CI/CD

Para usar CodeGuardian MCP en pipelines de CI/CD, puedes crear un script que ejecute el análisis y falle si se encuentran errores críticos:

```bash
#!/bin/bash
# ci-analysis.sh

# Ejecutar análisis
result=$(mcp call codeguardian analyze_project '{"project_path": ".", "config": {"output_format": "json"}}')

# Extraer número de errores
errors=$(echo "$result" | jq '.data.summary.errors_found')

# Fallar si hay errores
if [ "$errors" -gt 0 ]; then
    echo "❌ Se encontraron $errors errores críticos"
    exit 1
else
    echo "✅ Análisis completado sin errores críticos"
    exit 0
fi
```

## 8. Análisis Incremental

Para proyectos grandes, puedes analizar solo archivos modificados:

```json
{
  "name": "analyze_project",
  "arguments": {
    "project_path": ".",
    "config": {
      "include_patterns": [
        "src/modified-file1.py",
        "src/modified-file2.ts"
      ],
      "cache": {
        "enabled": true,
        "ttl_hours": 24
      }
    }
  }
}
```

## 9. Análisis Específico por Tipo de Error

### Solo Errores de Sintaxis
```json
{
  "name": "analyze_project",
  "arguments": {
    "project_path": "./src",
    "config": {
      "rules": {
        "syntax_check": true,
        "import_validation": false,
        "path_validation": false,
        "dependency_check": false
      }
    }
  }
}
```

### Solo Validación de Imports
```json
{
  "name": "analyze_project",
  "arguments": {
    "project_path": "./src",
    "config": {
      "rules": {
        "syntax_check": false,
        "import_validation": true,
        "path_validation": false,
        "dependency_check": false
      }
    }
  }
}
```

## 10. Manejo de Errores

### Error de Configuración Inválida
```json
{
  "success": false,
  "error": "Configuración inválida: languages debe ser un array de strings válidos"
}
```

### Error de Proyecto No Encontrado
```json
{
  "success": false,
  "error": "El directorio del proyecto no existe: /path/to/nonexistent"
}
```

### Error de Timeout
```json
{
  "success": false,
  "error": "Timeout durante el análisis del archivo large-file.py después de 30000ms"
}
```
